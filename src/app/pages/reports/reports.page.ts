import { PermitDetailsComponent } from 'src/app/components/permit-details/permit-details.component';
import { AppConstants, PermitStatus, PermitStatusProgress, PermitUserRole } from 'src/app/shared/app-constants';
import { Component, NgZone, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
declare var Odometer: any;
import { FormControl, FormGroup, Validators } from '@angular/forms';
import {
  ResultType,
  UnviredCordovaSDK,
} from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import {
  Alert<PERSON>ontroller,
  LoadingController,
  ModalController,
} from '@ionic/angular';
import { Router } from '@angular/router';
import {
  AGENT_HEADER,
  DIVISION_HEADER,
  FACILITY_HEADER,
  PERMIT_HEADER,
  PERMIT_LOG,
  PERMIT_QUERY_CTX_HEADER,
  PERMIT_STAKEHOLDER,
  PERMIT_TYPE_HEADER,
  STRUCTURE_HEADER,
  USER_CONTEXT_HEADER,
} from 'src/app/data-models/data_classes';
import { DataService } from 'src/app/services/data.service';
import * as moment from 'moment';
import { TranslateService } from '@ngx-translate/core';
import { ReportFilterComponent } from 'src/app/components/report-filter/report-filter.component';
import { ReportsPermitsListComponent } from 'src/app/components/reports-permits-list/reports-permits-list.component';
@Component({
  selector: 'app-reports',
  templateUrl: './reports.page.html',
  styleUrls: ['./reports.page.scss'],
})
export class ReportsPage implements OnInit, OnDestroy {
  public permitHeaderData = {} as PERMIT_HEADER;
  public permitFilteredList: any[] = [];
  public permitList: any[] = [];
  public userFacility: string = '';
  public searchTerm: string = '';
  public isAdmin: boolean = false;
  public permitStatusList = [{ label: 'OPEN', value: 'OPEN' }, { label: 'IN_REVIEW', value: 'IN_REVIEW' }, { label: 'APPROVED', value: 'APPROVED' }, { label: 'ISSUED', value: 'ISSUED' }, { label: 'CLOSED', value: 'CLOSED' }, { label: 'CANCELLED', value: 'CANCELLED' }]
  public userHeader: USER_CONTEXT_HEADER;
  public userObj: any;
  public isShowLoadMoreButton: boolean = true;
  public permitQueryCtxHeader = new PERMIT_QUERY_CTX_HEADER();
  public summaryChartData = [];
  public permitTypeChartData = [];
  public vendorBarChatData = [];
  public permitStatusChartData = [];
  public timeLineChartData = [];
  private odometerInstances: { [key: string]: any } = {};
  public isLoading: boolean = true;

  gradient: boolean = false;
  showLegend: boolean = true;
  showLabels: boolean = false;
  isDoughnut: boolean = true;
  legendPosition: any = 'below';
  arcWidth: number = 0.5; // Controls the thickness of the doughnut ring (0.1 to 0.5)

  showXAxis = true;
  showYAxis = true;
  showXAxisLabel = false;
  showYAxisLabel = false;
  showGridLines = false;
  isshowBarLegend: boolean = false;
  permitTypeColors = ['#0f9ed5', '#a02b93', '#156082', '#e97132', '#196b24'];

  legend: boolean = true;
  animations: boolean = true;
  xAxis: boolean = true;
  yAxis: boolean = true;
  showseriesYAxisLabel: boolean = true;
  showseriesXAxisLabel: boolean = true;
  xAxisLabel: string = '';
  yAxisLabel: string = 'Permits';
  timeline: boolean = true;
  public isChartView: boolean = true;
  public allPermitList: any[] = [];

  // Function to format the x-axis ticks for weekly display
  weeklyTickFormatting = (val: string) => {
    // If the value contains a date range (e.g., "Jan 01 - Jan 07, 2023")
    if (val && val.includes(' - ')) {
      // Extract just the first part (e.g., "Jan 01")
      return val.split(' - ')[0];
    }
    return val;
  };


  constructor(
    public dataService: DataService,
    public unviredSDK: UnviredCordovaSDK,
    public modalController: ModalController,
    public loadingController: LoadingController,
    public alertController: AlertController,
    public translate: TranslateService,
    private ngZone: NgZone,
    private router: Router,
  ) { }

  async ngOnInit() {
    this.permitFilteredList = [];
    this.allPermitList = [];

    // Ensure odometer script is loaded
    this.ensureOdometerScriptLoaded();
  }

  private ensureOdometerScriptLoaded() {
    try {
      // Check if Odometer is already defined
      if (typeof Odometer === 'undefined') {
        console.log('Odometer not found, loading script...');
        // Create script element
        const script = document.createElement('script');
        script.src = 'assets/js/odometer.min.js'; // Try a different path
        script.async = true;
        script.onload = () => {
          console.log('Odometer script loaded successfully');
        };
        script.onerror = (error) => {
          console.error('Error loading Odometer script:', error);
        };
        document.head.appendChild(script);

        // Also load the CSS
        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = 'assets/css/odometer-theme-minimal.css'; // Try a different path
        document.head.appendChild(link);
      } else {
        console.log('Odometer already loaded');
      }
    } catch (error) {
      console.error('Error ensuring Odometer script is loaded:', error);
    }
  }

  ngOnDestroy() {
    // Clean up any resources
    this.odometerInstances = {};
  }

  private initOdometers() {
    console.log('Initializing odometers with data:', this.summaryChartData);

    // Wait for DOM to be ready
    setTimeout(() => {
      if (this.summaryChartData && this.summaryChartData.length > 0) {
        this.summaryChartData.forEach(data => {
          try {
            const el = document.getElementById(`odometer-${data.CATEGORY.toLowerCase()}`);
            if (el) {
              console.log(`Found element for ${data.CATEGORY}:`, el);

              // Create a new odometer instance
              if (typeof Odometer !== 'undefined') {
                console.log('Odometer is defined, creating instance');

                // Set initial text content to ensure element has a value
                el.textContent = '0';

                // Create odometer instance
                this.odometerInstances[data.CATEGORY] = new Odometer({
                  el: el,
                  value: 0,
                  format: 'd',
                  theme: 'minimal',
                  duration: 1500
                });

                // Force a small delay before updating
                setTimeout(() => {
                  const numValue = parseInt(data.VALUE) || 0;
                  console.log(`Updating odometer for ${data.CATEGORY} to ${numValue}`);
                  this.odometerInstances[data.CATEGORY].update(numValue);
                }, 100);
              } else {
                console.error('Odometer is not defined');
                el.textContent = data.VALUE;
              }
            } else {
              console.error(`Element not found: odometer-${data.CATEGORY.toLowerCase()}`);
            }
          } catch (error) {
            console.error(`Error initializing odometer for ${data.CATEGORY}:`, error);
          }
        });
      }
    }, 500);
  }

  private updateOdometers() {
    console.log('Updating odometers with data:', this.summaryChartData);

    if (this.summaryChartData && this.summaryChartData.length > 0) {
      this.summaryChartData.forEach(data => {
        try {
          if (this.odometerInstances[data.CATEGORY]) {
            const numValue = parseInt(data.VALUE) || 0;
            console.log(`Updating odometer for ${data.CATEGORY} to ${numValue}`);
            this.odometerInstances[data.CATEGORY].update(numValue);
          } else {
            console.warn(`Odometer instance not found for ${data.CATEGORY}, creating new instance`);
            // Try to create a new instance
            const el = document.getElementById(`odometer-${data.CATEGORY.toLowerCase()}`);
            if (el) {
              this.odometerInstances[data.CATEGORY] = new Odometer({
                el: el,
                value: 0,
                format: 'd',
                theme: 'minimal',
                duration: 1500
              });

              // Update with value
              const numValue = parseInt(data.VALUE) || 0;
              setTimeout(() => {
                this.odometerInstances[data.CATEGORY].update(numValue);
              }, 100);
            }
          }
        } catch (error) {
          console.error(`Error updating odometer for ${data.CATEGORY}:`, error);
        }
      });
    }
  }

  async ionViewDidEnter() {
    await this.getDashboardData();

    this.ngZone.run(async () => {
      let userAgentResult = await this.unviredSDK.dbExecuteStatement(
        `SELECT * FROM USER_CONTEXT_HEADER`
      );
      if (userAgentResult.type == ResultType.success) {
        if (userAgentResult?.data?.length > 0) {
          this.userObj = userAgentResult.data[0];
          this.userFacility = userAgentResult.data[0].CURRENT_FACILITY;
        }
      }
      await this.getPermits();
    });

  }

  async getDashboardData() {
    // Clear existing odometer instances
    this.odometerInstances = {};
    this.summaryChartData = [];
    this.permitTypeChartData = [];
    this.permitStatusChartData = [];
    this.vendorBarChatData = [];
    this.timeLineChartData = [];
    this.isLoading = true; // Show shimmer effect

    try {
      await this.dataService.getDashboard();
      let res = await this.dataService.getData('CHART_HEADER');

      if (!res || res.length === 0) {
        // No chart headers found, set loading to false and return
        this.isLoading = false;
        return;
      }

      res.forEach(async element => {
      switch (element.CHART_ID) {
        case 'SUMMARY':
          let summaryRes = await this.dataService.getData('CHART_AXIS', `CHART_ID='${element.CHART_ID}'`);
          this.summaryChartData = summaryRes;
          this.summaryChartData.forEach(element => {
            element.CATEGORY = element.CATEGORY.split(' ')[0];
          })
          this.summaryChartData.sort((a, b) => {
            const categoriesOrder = ['Total', 'Open', 'Overdue', 'Extended', 'Cancelled', 'Internal', 'External'];
            return categoriesOrder.indexOf(a.CATEGORY) - categoriesOrder.indexOf(b.CATEGORY);
          });
          this.summaryChartData.forEach(element => {
            switch (element.CATEGORY) {
              case 'Overdue':
                element['colorClass'] = 'overdue'
                break;
              case 'Open':
                element['colorClass'] = 'open'
                break;
              case 'Cancelled':
                element['colorClass'] = 'cancelled'
                break;
              case 'Extended':
                element['colorClass'] = 'extended'
                break;
              default:
                break;
            }
          });

          // Initialize odometers after data is loaded
          setTimeout(() => {
            console.log('Initializing odometers with data:', this.summaryChartData);
            this.initOdometers();
          }, 100);
          break;

        case 'PERMIT_TYPE':
          let typeRes = await this.dataService.getData('CHART_AXIS', `CHART_ID='${element.CHART_ID}'`);
          this.permitTypeChartData = typeRes;
          this.permitTypeChartData.forEach(element => {
            element['name'] = element.CATEGORY;
            element['value'] = element.VALUE;
          });
          break;

        case 'VENDOR':
          let vendorRes = await this.dataService.getData('CHART_AXIS', `CHART_ID='${element.CHART_ID}'`);
          this.vendorBarChatData = vendorRes;
          this.vendorBarChatData.forEach(element => {
            element['name'] = element.CATEGORY;
            element['value'] = element.VALUE;
          });
          break;

        case 'PERMIT_STATUS':
          let statusRes = await this.dataService.getData('CHART_AXIS', `CHART_ID='${element.CHART_ID}'`);
          this.permitStatusChartData = statusRes;
          this.permitStatusChartData.forEach(element => {
            element['name'] = element.CATEGORY;
            element['value'] = element.VALUE;
          });
          break;

        case 'TIMELINE':
            // Get the original timeline data
            let timeLineRes = await this.dataService.getData('CHART_SERIES', `CHART_ID='${element.CHART_ID}'`);

            // Group the data by weeks
            const weeklyTimeLineData = this.groupTimelineDataByWeeks(timeLineRes);

            // Extract unique series names
            const uniqueSeries = [];
            const seriesMap = new Map();

            weeklyTimeLineData.forEach(item => {
              if (!seriesMap.has(item.SERIES)) {
                seriesMap.set(item.SERIES, true);
                uniqueSeries.push(item.SERIES);
              }
            });

            // Create the series data structure for the chart
            let series = uniqueSeries.map(seriesName => {
              return {
                name: seriesName,
                series: weeklyTimeLineData
                  .filter(item => item.SERIES === seriesName)
                  .map(filteredItem => {
                    return {
                      value: filteredItem.VALUE,
                      name: filteredItem.CATEGORY
                    };
                  })
                  // Sort by date (assuming the category is now in the format "MMM DD - MMM DD, YYYY")
                  .sort((a, b) => {
                    // Extract the start date from the week range
                    const getStartDate = (str) => {
                      const parts = str.split(' - ')[0];
                      // Convert "MMM DD" to a date
                      const currentYear = new Date().getFullYear();
                      return new Date(`${parts}, ${currentYear}`);
                    };

                    return getStartDate(a.name).getTime() - getStartDate(b.name).getTime();
                  })
              };
            });

            this.timeLineChartData = series;
            break;
        default:
          break;
      }
      // Set loading to false and dismiss any existing loader
      this.isLoading = false;
      this.loadingController.dismiss().catch(() => {});

      // Initialize odometers after all data is loaded
      setTimeout(() => {
        console.log('Initializing odometers after data load');
        this.initOdometers();

        // Try again after a longer delay to ensure DOM is ready
        setTimeout(() => {
          console.log('Retrying odometer initialization');
          this.updateOdometers();
        }, 1000);
      }, 500);
      });
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      this.isLoading = false;
    }
  }

  async showSummaryPermitDetails(summary: any) {
    await this.fetchStatusBasedPermits(summary.CATEGORY)
  }

  async backToCharts() {
    // First set loading to true to show shimmer effect
    this.isLoading = true;

    // Reset odometer instances
    this.odometerInstances = {};

    // Set chart view to true
    this.isChartView = true;

    // Reload dashboard data
    setTimeout(async () => {
      await this.getDashboardData();
    }, 100);
  }

  async fetchStatusBasedPermits(status: string) {
    // Show loading state
    this.isLoading = true;

    let whereCondition = '';

    this.permitList = [];
    switch (status) {
      case 'Total':
        whereCondition = `FACILITY_ID = '${this.userFacility}'`;
        break;
      case 'Open':
        whereCondition = `FACILITY_ID = '${this.userFacility}' AND STATUS = 'OPEN'`;
        break;
      case 'Cancelled':
        whereCondition = `FACILITY_ID = '${this.userFacility}' AND STATUS = 'CANCELLED'`;
        break;
      case 'Extended':
        whereCondition = `FACILITY_ID = '${this.userFacility}' AND IS_EXTENDED = 'true'`;
        break;
      default:
        break;
    }
    let res = await this.dataService.getData('PERMIT_HEADER', whereCondition, 'PERMIT_NO DESC');

    // Hide loading state
    this.isLoading = false;

    if (res && res.length > 0) {
      this.permitFilteredList = res;
      this.isChartView = false;
    } else {
      this.permitFilteredList = [];
      this.isChartView = false;
    }
  }

  async showAlert(title: string, message: string) {
    const alert = await this.alertController.create({
      header: title,
      message: message,
      buttons: ['OK'],
    });
    await alert.present();
  }

  async displayPleaseWaitLoader(messageReceived) {
    const loading = await this.loadingController.create({
      message: messageReceived,
      backdropDismiss: false,
    });
    await loading.present();
  }

  async getPermits() {
    this.permitFilteredList = [];
    this.allPermitList = [];
    let header = new PERMIT_QUERY_CTX_HEADER();
    header.FACILITY_ID = this.userFacility;
    let reportHeader: any = {};
    let reportHeaderToServer: any = {};
    reportHeader['PERMIT_QUERY_CTX_HEADER'] = header
    reportHeaderToServer['PERMIT_QUERY_CTX'] = [reportHeader];
    let permitHeaderResponse = await this.dataService.getReportPermitList(
      reportHeaderToServer
    );
    let infoMsg = this.dataService.handleInfoMessage(permitHeaderResponse);
    if (permitHeaderResponse.type == ResultType.success) {
      if (infoMsg && infoMsg?.length > 0) {
        // Check if this is a "no permits found" message and we want to show zero data screen
        if (this.hasNoData() && infoMsg.toLowerCase().includes('no permits')) {
          // Don't show the popup, let the zero data screen handle it
          console.log('No permits found, showing zero data screen instead of popup');
        } else {
          await this.showAlert('Info', infoMsg);
        }
      } else {
        if (permitHeaderResponse && permitHeaderResponse.data) {
          this.permitList = permitHeaderResponse.data;

          let data = permitHeaderResponse.data;
          if (data && data.PERMIT && data.PERMIT.length > 0) {
            for (let i = 0; i < data.PERMIT.length; i++) {
              this.permitFilteredList.push(data.PERMIT[i].PERMIT_HEADER);
              this.allPermitList.push(data.PERMIT[i]);
            }
          }
        }
      }
    }
  }


  async showPermitDetails(permit: any) {
    this.allPermitList.forEach((ele, i) => {
    if(ele.PERMIT_HEADER && ele.PERMIT_HEADER.PERMIT_NO && ele.PERMIT_HEADER.PERMIT_NO === permit.PERMIT_NO){
    localStorage.setItem('permitDetailsFromreport', JSON.stringify(ele));
    }
    });

    localStorage.setItem('selectedPermit', JSON.stringify(permit));
    let userRole: string = '';
    let isShowModal: boolean = true;
    let isProcessSwitchCase: boolean = true;

    // Show inspection details modal based on status
    if (isShowModal) {
      const modal = await this.modalController.create({
        component: PermitDetailsComponent,
        cssClass: 'full-screen-modal',
        backdropDismiss: false,
        id: 'permit-details',
        componentProps: {
          userRole: userRole,
          permit: permit,
          isReport: true
        },
      });
      modal.onDidDismiss().then(async (result) => { });
      return await modal.present();
    }
  }

  getLabelBasedOnStatus(status: string) {
    let label = '';
    switch (status.trim()) {
      case PermitStatus.OPEN:
        label = 'Open';
        break;
      case PermitStatus.IN_REVIEW:
        label = 'In Review';
        break;
      case PermitStatus.CANCELLED:
        label = 'Canceled';
        break;
      case PermitStatus.APPROVED:
        label = 'Approved';
        break;
      case PermitStatus.ISSUED:
        label = 'Issued';
        break;
      case PermitStatus.CLOSED:
        label = 'Closed';
        break;
      default:
        label = 'Open';
        break;
    }
    return label;
  }

  GetProgressPercentCssClass(status: string) {
    let cssClass = '';
    switch (status.trim()) {
      case PermitStatus.OPEN:
        cssClass = 'orange';
        break;
      case PermitStatus.IN_REVIEW:
        cssClass = 'orange';
        break;
      case PermitStatus.CANCELLED:
        cssClass = 'light-Grey';
        break;
      case PermitStatus.CLOSED:
        cssClass = 'darak-Green';
        break;
      case PermitStatus.APPROVED:
        cssClass = 'light-Green';
        break;
      case PermitStatus.ISSUED:
        cssClass = 'darak-Green';
        break;
      default:
        cssClass = 'orange';
        break;
    }
    return cssClass;
  }

  GetBorderCssClass(status: string) {
    let cssClass = '';
    switch (status.trim()) {
      case PermitStatus.OPEN:
        cssClass = 'orange-border';
        break;
      case PermitStatus.IN_REVIEW:
        cssClass = 'orange-border';
        break;
      case PermitStatus.CANCELLED:
        cssClass = 'light-Grey-border';
        break;
      case PermitStatus.CLOSED:
        cssClass = 'darak-Green-border';
        break;
      case PermitStatus.APPROVED:
        cssClass = 'light-Green-border';
        break;
      case PermitStatus.ISSUED:
        cssClass = 'darak-Green-border';
        break;
      default:
        cssClass = 'orange-border';
        break;
    }
    return cssClass;
  }

  isPermitIsExpired(permit: any) {
    permit.IS_EXTENDED = (permit.IS_EXTENDED == true || permit.IS_EXTENDED == 'true') ? 'true' : 'false';
    if(permit.STATUS == 'CLOSED' || permit.STATUS == 'CANCELLED'){
      return false;
    } else if ((permit.IS_EXTENDED == true || permit.IS_EXTENDED == 'true') && this.isExpired(permit.EXTENSION_DATE)) {
      return true;
      // permit has expired
    } else if ((permit.IS_EXTENDED == false || permit.IS_EXTENDED == 'false') && this.isExpired(permit.EXPIRY_DATE)) {
      return true;
      // permit has expired
    } else {
      return false;
      // permit is valid
    }
  }

  isExpired(expiryDate: any): boolean {
    const expiry = new Date(expiryDate);
    const now = new Date();
    return now > expiry;
  }

  permitStatusProgress(status: string) {
    let percentage = 0;
    switch (status.trim()) {
      case PermitStatus.OPEN:
        percentage = PermitStatusProgress.OPEN;
        break;
      case PermitStatus.IN_REVIEW:
        percentage = PermitStatusProgress.IN_REVIEW;
        break;
      case PermitStatus.APPROVED:
        percentage = PermitStatusProgress.APPROVED;
        break;
      case PermitStatus.ISSUED:
        percentage = PermitStatusProgress.ISSUED;
        break;
      case PermitStatus.CLOSED:
        percentage = PermitStatusProgress.COMPLETED;
        break;
      case PermitStatus.CANCELLED:
        percentage = PermitStatusProgress.CANCELLED;
        break;
      default:
        percentage = PermitStatusProgress.OPEN;
        break;
    }
    return percentage;
  }

  async onPermitTypeSelect(data): Promise<void> {
    // Show loading state
    this.isLoading = true;

    this.permitList = [];
    let res = await this.dataService.getData('PERMIT_HEADER', `PERMIT_TYPE = '${data?.name}'`, 'PERMIT_DATE DESC');

    // Hide loading state
    this.isLoading = false;

    if (res && res.length > 0) {
      let tempVal = data?.name.toLowerCase();
      tempVal = tempVal.charAt(0).toUpperCase() + tempVal.slice(1);

      this.permitFilteredList = res;
      this.isChartView = false;
    } else {
      this.permitFilteredList = [];
      this.isChartView = false;
    }
  }

  async onPermitStatusSelect(data): Promise<void> {
    // Show loading state
    this.isLoading = true;

    this.permitList = [];
    let res = await this.dataService.getData('PERMIT_HEADER', `STATUS = '${data?.name}'`, 'STATUS ASC');

    // Hide loading state
    this.isLoading = false;

    if (res && res.length > 0) {
      let tempVal = data?.name.toLowerCase();
      tempVal = tempVal.charAt(0).toUpperCase() + tempVal.slice(1);
      this.permitFilteredList = res;
      this.isChartView = false;
    } else {
      this.permitFilteredList = [];
      this.isChartView = false;
    }
  }

  async onVendorPermitsSelect(data): Promise<void> {
    // Show loading state
    this.isLoading = true;

    this.permitList = [];
    let res = await this.dataService.getData('PERMIT_HEADER', `STATUS = '${data?.name}'`, 'STATUS ASC');

    // Hide loading state
    this.isLoading = false;

    if (res && res.length > 0) {
      let tempVal = data?.name.toLowerCase();
      tempVal = tempVal.charAt(0).toUpperCase() + tempVal.slice(1);
      this.permitFilteredList = res;
      this.isChartView = false;
    } else {
      this.permitFilteredList = [];
      this.isChartView = false;
    }
  }

  async onTimeLinePermitsSelect(data): Promise<void> {
    // Show loading state
    this.isLoading = true;

    this.permitList = [];
    let res = await this.dataService.getData('PERMIT_HEADER', `PERMIT_TYPE = '${data?.series}'`, 'STATUS ASC');

    // Hide loading state
    this.isLoading = false;

    if (res && res.length > 0) {
      let tempVal = data?.series.toLowerCase();
      tempVal = tempVal.charAt(0).toUpperCase() + tempVal.slice(1);
      this.permitFilteredList = res;
      this.isChartView = false;
    } else {
      this.permitFilteredList = [];
      this.isChartView = false;
    }
  }

  // Check if there's no data to display
  hasNoData(): boolean {
    // Check if summary data exists and has meaningful values
    const hasSummaryData = this.summaryChartData && this.summaryChartData.length > 0 &&
                          this.summaryChartData.some(item => item.VALUE && parseInt(item.VALUE) > 0);

    // Check if any chart data exists
    const hasChartData = (this.permitTypeChartData && this.permitTypeChartData.length > 0) ||
                        (this.permitStatusChartData && this.permitStatusChartData.length > 0) ||
                        (this.vendorBarChatData && this.vendorBarChatData.length > 0) ||
                        (this.timeLineChartData && this.timeLineChartData.length > 0);

    // Debug logging
    console.log('hasNoData() check:', {
      summaryChartData: this.summaryChartData,
      permitTypeChartData: this.permitTypeChartData,
      permitStatusChartData: this.permitStatusChartData,
      vendorBarChatData: this.vendorBarChatData,
      timeLineChartData: this.timeLineChartData,
      hasSummaryData,
      hasChartData,
      result: !hasSummaryData && !hasChartData
    });

    // Return true if no meaningful data exists
    const result = !hasSummaryData && !hasChartData;

    // Temporary override for testing - force zero data screen
    return true;

    return result;
  }

  // Navigate to create permit page
  navigateToCreatePermit() {
    // Navigate to permits page where user can create a new permit
    this.router.navigate(['/permits']);
  }

  // Refresh data
  async refreshData() {
    this.isLoading = true;
    await this.getDashboardData();
    await this.getPermits();
  }

  /**
   * Groups timeline data by weeks
   * @param timelineData The original timeline data with daily entries
   * @returns Timeline data grouped by weeks
   */
  private groupTimelineDataByWeeks(timelineData: any[]): any[] {
    // Create a map to store weekly aggregated data
    const weeklyDataMap = new Map();

    // Process each item in the timeline data
    timelineData.forEach(item => {
      // Parse the date from the category (assuming format is a date string)
      const date = new Date(item.CATEGORY);
      if (isNaN(date.getTime())) {
        // If date is invalid, skip this item
        console.warn(`Invalid date format: ${item.CATEGORY}`);
        return;
      }

      // Get the week start date (Sunday)
      const weekStartDate = new Date(date);
      weekStartDate.setDate(date.getDate() - date.getDay());

      // Format the week as "MMM DD - MMM DD, YYYY" (e.g., "Jan 01 - Jan 07, 2023")
      const weekEndDate = new Date(weekStartDate);
      weekEndDate.setDate(weekStartDate.getDate() + 6);

      const weekStartStr = weekStartDate.toLocaleDateString('en-US', { month: 'short', day: '2-digit' });
      const weekEndStr = weekEndDate.toLocaleDateString('en-US', { month: 'short', day: '2-digit', year: 'numeric' });
      const weekKey = `${weekStartStr} - ${weekEndStr}`;

      // Create a unique key for each series and week
      const mapKey = `${item.SERIES}|${weekKey}`;

      // Add or update the value for this week
      if (weeklyDataMap.has(mapKey)) {
        weeklyDataMap.get(mapKey).VALUE += parseInt(item.VALUE) || 0;
      } else {
        weeklyDataMap.set(mapKey, {
          SERIES: item.SERIES,
          CATEGORY: weekKey,
          VALUE: parseInt(item.VALUE) || 0
        });
      }
    });

    // Convert the map back to an array
    return Array.from(weeklyDataMap.values());
  }
}