  <!-- Loading state with shimmer effect -->
  <div *ngIf="isLoading" class="shimmer-container" style="padding-top: 20px;">
    <!-- Shimmer for summary cards and charts -->
    <ion-grid>
      <ion-row>
        <ion-col>
          <div class="grid-container">
            <div class="shimmer-card" *ngFor="let i of [1,2,3,4,5]"></div>
          </div>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col size="6">
          <div class="shimmer-chart"></div>
        </ion-col>
        <ion-col size="6">
          <div class="shimmer-chart"></div>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col size="6">
          <div class="shimmer-chart"></div>
        </ion-col>
        <ion-col size="6">
          <div class="shimmer-chart"></div>
        </ion-col>
      </ion-row>
    </ion-grid>
  </div>

  <!-- Zero Data State -->
  <div *ngIf="!isLoading && isChartView && hasNoData()" class="zero-data-container">
    <div class="zero-data-content">
      <div class="zero-data-icon">
        <ion-icon name="bar-chart-outline"></ion-icon>
      </div>
      <h2 class="zero-data-title">{{ "No Reports Available" | translate }}</h2>
      <p class="zero-data-message">{{ "There are currently no permits to display in the reports. Create your first permit to see analytics and insights here." | translate }}</p>
      <div class="zero-data-actions">
        <ion-button fill="solid" color="primary" (click)="navigateToCreatePermit()">
          <ion-icon slot="start" name="add-outline"></ion-icon>
          {{ "Create First Permit" | translate }}
        </ion-button>
        <ion-button fill="outline" color="medium" (click)="refreshData()">
          <ion-icon slot="start" name="refresh-outline"></ion-icon>
          {{ "Refresh" | translate }}
        </ion-button>
      </div>
    </div>
  </div>

  <!-- Charts View with Data -->
  <div *ngIf="!isLoading && isChartView && !hasNoData()" style="margin-top: 0; padding-top: 10px;">
    <ion-grid style="margin-top: 0; padding-top: 0;">
      <ion-row style="margin-top: 0; padding-top: 0;">
        <ion-col style="padding-top: 0;">
          <div class="grid-container">
            <div class="grid-item" *ngFor="let data of summaryChartData">
              <ion-card style="margin-top: 0;" (click)="showSummaryPermitDetails(data); $event.stopPropagation();">

          <ion-row>
            <ion-col text-center>
              <h6 style="font-size: small;margin-bottom: 0px;margin-top: 10px;"><b>{{data.CATEGORY}}</b></h6>
            </ion-col>
          </ion-row>

          <ion-row>
            <ion-col text-center>
              <h6 style="font-size: xx-large;margin-top: 0px;margin-bottom: 0px;" [class]="data.colorClass">
                <b>
                  <div id="odometer-{{data.CATEGORY.toLowerCase()}}" class="odometer">0</div>
                </b>
              </h6>
            </ion-col>
          </ion-row>
              </ion-card>
            </div>
          </div>
        </ion-col>
      </ion-row>
      <ion-row class="chart-row">
        <ion-col size="6">
          <ion-card>
            <ion-card-header>
              <ion-card-title>Permits by Type</ion-card-title>
            </ion-card-header>
            <ion-card-content style="padding: 0px !important;">
              <ngx-charts-pie-chart legendTitle="" [results]="permitTypeChartData" [gradient]="gradient"
                (select)="onPermitTypeSelect($event)" [legend]="showLegend" [scheme]="'picnic'"
                [legendPosition]="legendPosition" [labels]="showLabels" [doughnut]="isDoughnut"
                [arcWidth]="arcWidth"> <!-- Using arcWidth property from TypeScript -->
              </ngx-charts-pie-chart>
            </ion-card-content>
          </ion-card>
        </ion-col>

        <ion-col size="6" style="text-align: center;">
          <ion-card>
            <ion-card-header>
              <ion-card-title>Permits by Status</ion-card-title>
            </ion-card-header>
            <ion-card-content style="padding: 0px !important;">
              <ngx-charts-pie-chart legendTitle="" [results]="permitStatusChartData" [gradient]="gradient"
                (select)="onPermitStatusSelect($event)" [legend]="showLegend" [scheme]="'vivid'"
                [legendPosition]="legendPosition" [labels]="showLabels" [doughnut]="isDoughnut"
                [arcWidth]="arcWidth"> <!-- Using arcWidth property from TypeScript -->
              </ngx-charts-pie-chart>
            </ion-card-content>
          </ion-card>
        </ion-col>
      </ion-row>

      <ion-row>
        <ion-col size="6" style="text-align: center;">
          <ion-card>
            <ion-card-header>
              <ion-card-title>Permits by Vendor</ion-card-title>
            </ion-card-header>
            <ion-card-content style="padding: 0px !important;">
              <ngx-charts-bar-vertical legendTitle="" [results]="vendorBarChatData" [gradient]="gradient"
                (select)="onVendorPermitsSelect($event)" [xAxis]="showXAxis" [showGridLines]="showGridLines"
                [yAxis]="showYAxis" [legend]="showLegend" [showXAxisLabel]="showXAxisLabel"
                [showYAxisLabel]="showYAxisLabel" [legendPosition]="legendPosition">
              </ngx-charts-bar-vertical>
            </ion-card-content>
          </ion-card>
        </ion-col>

        <ion-col size="6" style="text-align: center;">
          <ion-card>
            <ion-card-header>
              <ion-card-title>Permits by Timeline</ion-card-title>
            </ion-card-header>
            <ion-card-content style="padding: 0px !important;">
              <ngx-charts-line-chart legendTitle="" [showXAxisLabel]="showseriesXAxisLabel"
                [showYAxisLabel]="showseriesYAxisLabel" [xAxis]="xAxis" [yAxis]="yAxis" [xAxisLabel]="xAxisLabel"
                (select)="onTimeLinePermitsSelect($event)" [yAxisLabel]="yAxisLabel" [timeline]="timeline"
                [results]="timeLineChartData" [legendPosition]="legendPosition" [legend]="showLegend"
                [autoScale]="true" [xAxisTickFormatting]="weeklyTickFormatting">
              </ngx-charts-line-chart>
            </ion-card-content>
          </ion-card>
        </ion-col>
      </ion-row>
    </ion-grid>
  </div>

  <div *ngIf="!isLoading && !isChartView" class="permits-content">
    <div class="back-to-charts">
      <ion-button fill="clear" (click)="backToCharts()">
        <ion-icon slot="start" name="arrow-back-outline"></ion-icon>
        Back to Charts
      </ion-button>
    </div>
    <div class="permits-table-wrapper">
      <div class="permits-table">
        <div class="table-header">
          <div class="header-cell status-cell">Status</div>
          <div class="header-cell permit-no-cell">Permit No.</div>
          <div class="header-cell desc-cell">Description</div>
          <div class="header-cell type-cell">Type</div>
          <div class="header-cell date-cell">Start Date</div>
          <div class="header-cell date-cell">End Date</div>
          <div class="header-cell requester-cell">Requested By</div>
          <div class="header-cell actions-cell">Actions</div>
        </div>

        <!-- Show filtered results when available -->
        <div class="table-row"
             *ngFor="let permit of permitFilteredList"
             (click)="showPermitDetails(permit)">
          <div class="table-cell status-cell" data-label="Status">
            <div class="status-wrapper">
              <div class="status-badge outline-badge" [ngClass]="GetProgressPercentCssClass(permit.STATUS)">
                {{ getLabelBasedOnStatus(permit.STATUS) }}
              </div>
              <div *ngIf="permit.IS_EXTENDED == 'true' || permit.IS_EXTENDED == true" class="extended-badge">Extended</div>
              <div *ngIf="isPermitIsExpired(permit)" class="expired-badge">Expired</div>
            </div>
          </div>
          <div class="table-cell permit-no-cell" data-label="Permit No.">{{ permit.PERMIT_NO }}</div>
          <div class="table-cell desc-cell" data-label="Description">{{ permit.DESCRIPTION }}</div>
          <div class="table-cell type-cell" data-label="Type">{{ permit.TYPE_DESC || permit.PERMIT_TYPE }}</div>
          <div class="table-cell date-cell" data-label="Start Date">
            {{ permit.PERMIT_DATE | GetFullDateByTimestampPipeReport | async }}
          </div>
          <div class="table-cell date-cell" data-label="End Date" [ngClass]="{'expired-date': isPermitIsExpired(permit)}">
            <div class="date-with-icon">
              {{ permit.EXPIRY_DATE | GetFullDateByTimestampPipeReport | async }}
              <ion-icon *ngIf="permit.IS_EXTENDED === 'true' || permit.IS_EXTENDED === true"
                        name="time-outline"
                        class="extension-icon"
                        title="Permit has been extended">
              </ion-icon>
            </div>
          </div>
          <div class="table-cell requester-cell" data-label="Requested By">
            {{ permit.REQUESTED_BY | GetRequestedByNamePipe | async }}, {{ permit.REQUESTED_ON | date }}
          </div>
          <div class="table-cell actions-cell" data-label="Actions">
            <ion-button fill="clear" color="primary" (click)="$event.stopPropagation(); showPermitDetails(permit)">
              <ion-icon name="eye-outline"></ion-icon>
            </ion-button>
          </div>
        </div>
      </div>

      <!-- Show empty state message when filtered list is empty -->
      <div *ngIf="permitFilteredList.length === 0" class="no-results-message">
        <div class="no-results-content">
          <ion-icon name="document-text-outline"></ion-icon>
          <p>No Reports Found</p>
        </div>
      </div>
    </div>
  </div>