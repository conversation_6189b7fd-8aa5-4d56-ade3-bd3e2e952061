  <!-- Tree View Container -->
  <div class="tree-container">
    <!-- Search and Action Bar -->
    <div class="search-card">
      <div class="search-row">
        <div class="search-wrapper">
          <ion-icon name="search-outline"></ion-icon>
          <input type="text" placeholder="{{ 'Search facilities...' | translate }}" [(ngModel)]="facilitySearchTerm" (input)="filterFacilities()">
        </div>
        <div class="action-buttons">
          <button class="add-btn" (click)="addFacility()">
            <ion-icon name="add-outline"></ion-icon>
            {{ "Add Facility" | translate }}
          </button>
        </div>
      </div>
    </div>

    <!-- Tree Structure -->
    <div class="tree-content">
      <!-- Facilities (Root Level) -->
      <div *ngFor="let facility of facilitiesArray" class="tree-node facility-node">
        <!-- Facility Item -->
        <div class="tree-item" [class.selected]="facility.NAME === currentFacilitySelected.NAME" (click)="selectedOption('facilities', facility)">
          <div class="item-content">
            <div class="expand-icon" [class.expanded]="isFacilityExpanded(facility.FACILITY_ID)" [class.disabled]="!hasDivisions(facility.FACILITY_ID)" (click)="hasDivisions(facility.FACILITY_ID) && toggleFacility(facility, $event)">
              <ion-icon name="chevron-down-outline" *ngIf="isFacilityExpanded(facility.FACILITY_ID)"></ion-icon>
              <ion-icon name="chevron-forward-outline" *ngIf="!isFacilityExpanded(facility.FACILITY_ID)"></ion-icon>
            </div>
            <ion-icon name="folder-outline" class="item-icon facility-icon"></ion-icon>
            <span class="item-label">{{facility.NAME}}</span>
            <div class="item-actions">
              <button class="badge-button add-division-badge" *ngIf="isFacilityExpanded(facility.FACILITY_ID) && getSelectedFcilityProperties()" (click)="addDivision(); $event.stopPropagation()">
                {{ "Add Division" | translate }}
              </button>
              <ion-button fill="clear" class="edit-button" (click)="editFacility(facility); $event.stopPropagation()">
                <i slot="icon-only" class="fal fa-pen edit-icon"></i>
              </ion-button>
            </div>
          </div>
        </div>

        <!-- Divisions (Second Level) - Only show when facility is expanded -->
        <div *ngIf="isFacilityExpanded(facility.FACILITY_ID)" class="tree-children">
          <!-- Shimmer for loading divisions -->
          <div *ngIf="isLoadingDivisions" class="shimmer-container">
            <div *ngFor="let i of [1,2,3]" class="shimmer-item">
              <div class="shimmer-line shimmer-icon"></div>
              <div class="shimmer-line shimmer-text"></div>
            </div>
          </div>

          <!-- Division Items -->
          <div *ngFor="let division of divisionsArray" class="tree-node division-node">
            <!-- Division Item -->
            <div class="tree-item" [class.selected]="division.NAME === currentDivisionSelected.NAME" (click)="selectedOption('division', division)">
              <div class="item-content">
                <div class="expand-icon" [class.expanded]="isDivisionExpanded(division.DIVISION_ID)" [class.disabled]="!hasStructures(division.FACILITY_ID, division.DIVISION_ID)" (click)="hasStructures(division.FACILITY_ID, division.DIVISION_ID) && toggleDivision(division, $event)">
                  <ion-icon name="chevron-down-outline" *ngIf="isDivisionExpanded(division.DIVISION_ID)"></ion-icon>
                  <ion-icon name="chevron-forward-outline" *ngIf="!isDivisionExpanded(division.DIVISION_ID)"></ion-icon>
                </div>
                <ion-icon name="folder-outline" class="item-icon division-icon"></ion-icon>
                <!-- Division name (always visible) -->
                <span class="item-label">{{division.NAME}}</span>
                <div class="item-actions">
                  <ion-button fill="clear" class="action-button" *ngIf="isDivisionExpanded(division.DIVISION_ID) && tempStructuresArray.length > 0" (click)="toggleStructureSearch(division); $event.stopPropagation()">
                    <ion-icon name="search-outline"></ion-icon>
                  </ion-button>
                  <button class="badge-button add-structure-badge" *ngIf="isDivisionExpanded(division.DIVISION_ID) && getSelectedFcilityProperties()" (click)="addOrUpdateStructure(); $event.stopPropagation()">
                    {{ "Add Structure Tag" | translate }}
                  </button>
                  <ion-button fill="clear" class="action-button" *ngIf="isDivisionExpanded(division.DIVISION_ID) && isEnabledAddingStructure" (change)="structureBulkDataUpload(csvFile.files); $event.stopPropagation()">
                    <label [for]="'file-input-' + division.DIVISION_ID">
                      <ion-icon name="cloud-download-outline"></ion-icon>
                    </label>
                    <input #csvFile [id]="'file-input-' + division.DIVISION_ID" type="file" accept=".csv" />
                  </ion-button>
                  <ion-button fill="clear" class="edit-button" (click)="editDivision(division); $event.stopPropagation()">
                    <i slot="icon-only" class="fal fa-pen edit-icon"></i>
                  </ion-button>
                </div>
                <!-- Search input when search is active for this division -->
                <div class="search-wrapper structure-search-inline" *ngIf="showStructureSearch && division.DIVISION_ID === currentDivisionSelected.DIVISION_ID">
                  <ion-icon name="search-outline"></ion-icon>
                  <input type="text" placeholder="{{ 'Search...' | translate }}" [(ngModel)]="searchTerm" (input)="filterStructures()">
                  <ion-icon name="close-outline" class="clear-search" (click)="showStructureSearch = false; $event.stopPropagation()"></ion-icon>
                </div>
              </div>
            </div>

            <!-- Structures (Third Level) - Only show when division is expanded -->
            <div *ngIf="isDivisionExpanded(division.DIVISION_ID)" class="tree-children">
              <!-- Structure Search is now inline in the division row -->
              <!-- Shimmer for loading structures -->
              <div *ngIf="isLoadingStructures" class="shimmer-container">
                <div *ngFor="let i of [1,2,3]" class="shimmer-item">
                  <div class="shimmer-line shimmer-icon"></div>
                  <div class="shimmer-line shimmer-text-long"></div>
                </div>
              </div>

              <!-- Structure Items -->
              <div *ngFor="let structure of structuresArray; index as i" class="tree-node structure-node">
                <div class="tree-item" [class.selected]="structure === selectedStructure" (click)="selectStructure(structure)">
                  <div class="item-content">
                    <ion-icon name="document-text-outline" class="item-icon structure-icon"></ion-icon>
                    <div class="item-label">
                      <div class="structure-name">{{structure.NAME}}</div>
                      <div class="structure-details">{{structure.TAG}} | {{structure.STRUCT_TYPE}} - {{structure.TYPE_DESCRIPTION}}</div>
                    </div>
                    <div class="item-actions">
                      <ion-button fill="clear" class="edit-button" (click)="addOrUpdateStructure(structure); $event.stopPropagation()">
                        <i slot="icon-only" class="fal fa-pen edit-icon"></i>
                      </ion-button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Shimmer for loading facilities -->
      <div *ngIf="isLoadingFacilities" class="shimmer-container">
        <div *ngFor="let i of [1,2,3,4,5]" class="shimmer-item">
          <div class="shimmer-line shimmer-icon"></div>
          <div class="shimmer-line shimmer-text"></div>
        </div>
      </div>
    </div>
  </div>