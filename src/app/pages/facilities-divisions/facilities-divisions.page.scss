ion-label {
  font-size: 15px !important;
}

.iconWithin<PERSON><PERSON>on {
  color: white;
}

.margin-left {
  margin-left: 10px;
}

// Tree View Styles
.tree-container {
  padding: 0;
  height: 100%;
  overflow-y: auto;
  background-color: #f9f9f9;
}

// Search Card Styles (matching users.page)
.search-card {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  padding: 12px 16px;
  margin-bottom: 16px;
}

.search-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
}

.search-wrapper {
  display: flex;
  align-items: center;
  background-color: #f1f3f5;
  border-radius: 8px;
  padding: 6px 12px;
  flex: 1;
  max-width: 300px;
  height: 36px;

  ion-icon {
    color: #6c757d;
    margin-right: 8px;
    font-size: 16px;
  }

  input {
    border: none;
    background: transparent;
    outline: none;
    width: 100%;
    font-size: 14px;
    color: #495057;

    &::placeholder {
      color: #999;
    }
  }
}

.action-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
}

.add-btn {
  background-color: var(--ion-color-primary);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0 16px;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: background-color 0.2s;
  height: 36px;

  &:hover {
    background-color: var(--ion-color-primary-shade);
  }

  ion-icon {
    margin-right: 6px;
    font-size: 16px;
  }
}

.action-btn {
  background-color: #f1f3f5;
  color: #495057;
  border: none;
  border-radius: 8px;
  padding: 0 16px;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: background-color 0.2s;
  height: 36px;

  &:hover {
    background-color: #e9ecef;
  }

  ion-icon {
    margin-right: 6px;
    font-size: 16px;
  }
}

ion-button {
  --color: #666;
  --padding-start: 8px;
  --padding-end: 8px;
  height: 36px;
}

.tree-content {
  margin-top: 10px;
}

.tree-node {
  margin-bottom: 4px;
}

.tree-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start; /* Align to top for multi-line text */
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 40px; /* Minimum height */
  height: auto; /* Allow height to adjust to content */
  position: relative; /* For absolute positioning of action buttons */

  &:hover {
    background-color: rgba(0, 0, 0, 0.04);
  }

  &.selected {
    background-color: rgba(0, 98, 155, 0.08);
  }
}

.item-content {
  display: flex;
  align-items: center; /* Center align for better button alignment */
  flex: 1;
  position: relative;
  min-height: 36px; /* Minimum height */
  overflow: visible; /* Show all content */
  flex-wrap: wrap; /* Allow wrapping for long content */
  gap: 8px; /* Add gap between elements */
}

.item-actions {
  display: none;
  align-items: center;
  margin: 0;
  position: static;
  margin-left: 8px; /* Add margin to separate from text */
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 4px;
  padding: 0 2px;
  height: 24px;
  z-index: 10;
  flex-wrap: wrap; /* Allow wrapping if needed */
  gap: 4px; /* Add gap between action items */

  .tree-item.show-actions & {
    display: flex;
  }
}

.expand-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 4px;
  cursor: pointer;
  border-radius: 50%;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }

  ion-icon {
    font-size: 16px;
    color: #666;
    transition: transform 0.2s ease;
  }

  &.expanded ion-icon[name="chevron-down-outline"] {
    color: #00629b;
  }

  &.disabled {
    opacity: 0.3;
    cursor: default;

    &:hover {
      background-color: transparent;
    }
  }
}

.item-icon {
  margin-right: 8px;
  font-size: 18px;
}

.facility-icon {
  color: #ffc107; // Yellow folder
}

.facility-node .tree-item {
  min-height: 40px; /* Minimum height */
  height: auto; /* Allow height to adjust to content */
  padding-top: 8px; /* Add padding for better spacing */
  padding-bottom: 8px;
  align-items: flex-start; /* Align to top for multi-line text */

  .item-content {
    align-items: flex-start; /* Align to top for multi-line text */
  }
}

.division-icon {
  color: #4caf50; // Green folder
}

.division-node .tree-item {
  min-height: 40px; /* Minimum height */
  height: auto; /* Allow height to adjust to content */
  transition: height 0.2s ease;
  padding-top: 8px; /* Add padding for better spacing */
  padding-bottom: 8px;
  align-items: flex-start; /* Align to top for multi-line text */

  &.show-actions .item-content {
    /* Ensure there's enough space for both search and actions */
    padding-right: 40px;
  }

  .item-content {
    align-items: flex-start; /* Align to top for multi-line text */
  }
}

.structure-icon {
  color: #2196f3; // Blue document
}

.structure-node .tree-item {
  min-height: 40px; /* Minimum height, but allow expansion */
  height: auto; /* Allow height to adjust to content */
  overflow: visible; /* Show all content */
  display: flex;
  align-items: flex-start; /* Align to top for multi-line text */
  padding-top: 10px; /* Add padding for better spacing */
  padding-bottom: 10px;

  .structure-name {
    font-weight: 500;
    margin-bottom: 4px;
  }

  .structure-details {
    color: #666;
    font-size: 13px;
  }
}

.item-label {
  font-size: 14px;
  color: #333;
  white-space: normal; /* Allow text to wrap */
  overflow: visible; /* Show all text */
  flex: 0 1 auto; /* Don't grow, but can shrink */
  word-break: break-word; /* Break long words if needed */
}

.tree-children {
  margin-left: 24px;
  margin-top: 4px;
  padding-left: 12px;
}

.add-division-row {
  margin: 12px 0 4px;
  padding-left: 24px;
}

.add-division-button {
  --color: #4caf50;
  font-size: 13px;
  --padding-start: 8px;
  --padding-end: 8px;
  height: 32px;

  ion-icon {
    margin-right: 4px;
  }
}

/* Inline structure search styling */
.structure-search-inline {
  flex: 0 0 auto;
  width: 200px;
  margin-top: 4px; /* Add top margin to separate from actions */
  height: 32px;
  background-color: #f1f3f5;
  border-radius: 8px;
  order: 3; /* Place after actions */
  flex-basis: 100%; /* Force to new line */

  input {
    width: 130px; /* Limit input width */
  }

  .clear-search {
    cursor: pointer;
    color: #6c757d;
    font-size: 16px;

    &:hover {
      color: #495057;
    }
  }
}

.structure-search-wrapper {
  background-color: #f5f5f5;
  border-radius: 4px;
  height: 36px;
  margin-bottom: 8px;
  width: 100%;
}

// Badge-style buttons for Add Division and Add Structure Tag
.badge-button {
  background-color: var(--ion-color-primary);
  color: white;
  border: none;
  border-radius: 10px;
  padding: 3px 6px;
  font-size: 10px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  height: 18px;
  line-height: 12px;
  margin: 0 2px;
  display: inline-flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background-color: var(--ion-color-primary-shade);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  &:active {
    transform: translateY(0);
  }
}

.add-division-badge {
  background-color: #4caf50; // Green for divisions

  &:hover {
    background-color: #45a049;
  }
}

.add-structure-badge {
  background-color: #2196f3; // Blue for structures

  &:hover {
    background-color: #1976d2;
  }
}

.edit-button, .action-button {
  --padding-start: 4px;
  --padding-end: 4px;
  height: 24px;
  width: 24px;
  min-width: 24px; /* Ensure fixed width */
  max-width: 24px; /* Ensure fixed width */
  --background: transparent;
  --background-hover: rgba(0,0,0,0.05);
  --color: #666;
  margin: 0 1px;

  &::part(native) {
    padding: 0;
  }

  ion-icon, i {
    font-size: 14px;
  }

  &:hover {
    --color: #00629b;
  }
}

.fa-pen {
  color: #666;
  font-size: 14px !important;
}

.action-button {
  input {
    display: none;
  }

  label {
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    width: 100%;
    margin: 0;
  }

  ion-icon {
    font-size: 18px;
  }
}

.no-items-message {
  padding: 12px;
  color: #666;
  font-style: italic;
  font-size: 14px;
  text-align: center;
}

/* Shimmer effect styles */
.shimmer-container {
  padding: 8px 0;
}

.shimmer-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  margin-bottom: 8px;
}

.shimmer-line {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 4px;
}

.shimmer-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  margin-right: 12px;
}

.shimmer-text {
  height: 16px;
  width: 70%;
}

.shimmer-text-long {
  height: 16px;
  width: 85%;
}

@keyframes shimmer {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}